namespace Winmug.Core.Authentication;

/// <summary>
/// Represents OAuth 1.0a credentials for SmugMug API access
/// </summary>
public class OAuthCredentials
{
    public string ConsumerKey { get; set; } = string.Empty;
    public string ConsumerSecret { get; set; } = string.Empty;
    public string? AccessToken { get; set; }
    public string? AccessTokenSecret { get; set; }

    public bool IsAuthenticated => !string.IsNullOrEmpty(AccessToken) && !string.IsNullOrEmpty(AccessTokenSecret);
}

/// <summary>
/// Represents a request token response from SmugMug
/// </summary>
public class RequestTokenResponse
{
    public string Token { get; set; } = string.Empty;
    public string TokenSecret { get; set; } = string.Empty;
    public string AuthorizationUrl { get; set; } = string.Empty;
    public bool CallbackConfirmed { get; set; }
}

/// <summary>
/// Represents an access token response from SmugMug
/// </summary>
public class AccessTokenResponse
{
    public string Token { get; set; } = string.Empty;
    public string TokenSecret { get; set; } = string.Empty;
    public string? UserNickname { get; set; }
}

/// <summary>
/// Configuration options for SmugMug OAuth
/// Note: ConsumerKey/ConsumerSecret are your SmugMug API Key/Secret
/// Users authenticate directly with their SmugMug credentials
/// </summary>
public class SmugMugOAuthOptions
{
    public const string SectionName = "SmugMugOAuth";

    /// <summary>
    /// Your SmugMug API Key (serves as OAuth Consumer Key)
    /// </summary>
    public string ConsumerKey { get; set; } = string.Empty;

    /// <summary>
    /// Your SmugMug API Secret (serves as OAuth Consumer Secret)
    /// </summary>
    public string ConsumerSecret { get; set; } = string.Empty;

    public string RequestTokenUrl { get; set; } = "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken";
    public string AuthorizeUrl { get; set; } = "https://secure.smugmug.com/services/oauth/1.0a/authorize";
    public string AccessTokenUrl { get; set; } = "https://secure.smugmug.com/services/oauth/1.0a/getAccessToken";
    public string CallbackUrl { get; set; } = "oob"; // Out-of-band for desktop apps

    /// <summary>
    /// Base URL for API calls after authentication (used in OAuth signature generation)
    /// </summary>
    public string ApiBaseUrl { get; set; } = "https://api.smugmug.com/api/v2";

    /// <summary>
    /// Port for local OAuth callback server (when using automatic authentication)
    /// </summary>
    public int LocalCallbackPort { get; set; } = 8765;

    /// <summary>
    /// Access level: "Full" for private data, "Public" for public data only
    /// We need "Full" to access user's private photos
    /// </summary>
    public string Access { get; set; } = "Full"; // Full or Public

    /// <summary>
    /// Permissions: "Read" to download photos, "Add" to upload, "Modify" to edit
    /// We only need "Read" for downloading
    /// </summary>
    public string Permissions { get; set; } = "Read"; // Read, Add, or Modify
}
