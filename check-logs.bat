@echo off
echo WinMug Debug Log Viewer
echo ======================
echo.

set "LOG_DIR=%LOCALAPPDATA%\WinMug\Logs"
echo Log Directory: %LOG_DIR%
echo.

if exist "%LOG_DIR%" (
    echo ✓ Log directory exists
    echo.
    echo Available log files:
    dir "%LOG_DIR%\*.log" /B 2>nul
    echo.
    echo To view the latest log file:
    echo notepad "%LOG_DIR%\winmug-*.log"
    echo.
    echo Or open the log directory:
    echo explorer "%LOG_DIR%"
) else (
    echo ❌ Log directory doesn't exist yet. Start the WinMug application to create it.
)

echo.
echo Instructions to debug the Start Download button issue:
echo 1. Run the WinMug application
echo 2. Authenticate with SmugMug  
echo 3. Load your albums using Show my albums
echo 4. Select some albums using the checkboxes
echo 5. Try clicking the Start download button
echo 6. Check the logs to see what happened
echo.
echo The logs will show detailed information about:
echo - Whether the button click was registered
echo - Authentication status
echo - Target directory status
echo - Number of selected albums
echo - Button enabled/disabled state changes
echo - Any errors that occur
echo.
pause
