@echo off
echo WinMug Debug Log Viewer
echo ======================
echo.

set "LOG_DIR=%LOCALAPPDATA%\WinMug\Logs"
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TODAY=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%"
set "LOG_FILE=%LOG_DIR%\winmug-%TODAY%.log"

echo Log Directory: %LOG_DIR%
echo Today's Log File: %LOG_FILE%
echo.

if exist "%LOG_DIR%" (
    echo ✓ Log directory exists
    echo.
    
    if exist "%LOG_FILE%" (
        echo 📋 Today's log content (last 20 lines):
        echo ----------------------------------------
        powershell -Command "Get-Content '%LOG_FILE%' -Tail 20"
        echo ----------------------------------------
        echo.
        echo 🔍 To view full log file, run:
        echo notepad "%LOG_FILE%"
        echo.
        echo 📝 To monitor logs in real-time, run:
        echo powershell -Command "Get-Content '%LOG_FILE%' -Wait -Tail 10"
    ) else (
        echo ⚠ Today's log file doesn't exist yet. Start the WinMug application to generate logs.
    )
) else (
    echo ❌ Log directory doesn't exist yet. Start the WinMug application to create it.
)

echo.
echo 🚀 Instructions to debug the Start Download button issue:
echo 1. Run the WinMug application
echo 2. Authenticate with SmugMug
echo 3. Load your albums using 'Show my albums'
echo 4. Select some albums using the checkboxes
echo 5. Try clicking the 'Start download' button
echo 6. Check the logs using this script to see what happened
echo.
echo The logs will show detailed information about:
echo - Whether the button click was registered
echo - Authentication status
echo - Target directory status  
echo - Number of selected albums
echo - Button enabled/disabled state changes
echo - Any errors that occur
echo.
pause
