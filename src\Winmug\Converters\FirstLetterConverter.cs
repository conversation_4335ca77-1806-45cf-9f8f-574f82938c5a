using System;
using System.Globalization;
using System.Windows.Data;

namespace Winmug.Converters
{
    /// <summary>
    /// Converts a string to its first letter in uppercase. Used for profile picture placeholders.
    /// </summary>
    public class FirstLetterConverter : IValueConverter
    {
        public static readonly FirstLetterConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var text = value as string;
            if (string.IsNullOrEmpty(text))
                return "U"; // Default to "U" for User

            // Split by spaces to get first and last name initials
            var words = text.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (words.Length >= 2)
            {
                // Return first letter of first name + first letter of last name
                return $"{words[0].Substring(0, 1).ToUpper()}{words[1].Substring(0, 1).ToUpper()}";
            }

            // Single word - return first letter
            return text.Substring(0, 1).ToUpper();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
