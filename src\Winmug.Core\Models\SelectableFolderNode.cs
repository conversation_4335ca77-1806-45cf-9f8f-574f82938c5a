using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a selectable folder/album node in the tree structure
/// </summary>
public class SelectableFolderNode : INotifyPropertyChanged
{
    private bool _isSelected;
    private bool _isExpanded;
    private bool? _isChecked;

    public string NodeId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // "Folder", "Album", etc.
    public string UrlName { get; set; } = string.Empty;
    public string ParentNodeId { get; set; } = string.Empty;
    public string FullPath { get; set; } = string.Empty;
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
    public bool HasChildren { get; set; }
    
    // Album-specific properties (when Type == "Album")
    public string AlbumKey { get; set; } = string.Empty;
    public int ImageCount { get; set; }
    public long EstimatedSizeBytes { get; set; }
    public string Privacy { get; set; } = string.Empty;
    public bool AllowDownloads { get; set; } = true;
    public string WebUri { get; set; } = string.Empty;

    /// <summary>
    /// Child folders and albums
    /// </summary>
    public ObservableCollection<SelectableFolderNode> Children { get; set; } = new();

    /// <summary>
    /// Parent node reference
    /// </summary>
    public SelectableFolderNode? Parent { get; set; }

    /// <summary>
    /// Whether this node is expanded in the tree view
    /// </summary>
    public bool IsExpanded
    {
        get => _isExpanded;
        set
        {
            if (_isExpanded != value)
            {
                _isExpanded = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Whether this node is selected (for UI highlighting)
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (_isSelected != value)
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Three-state checkbox: true = checked, false = unchecked, null = indeterminate
    /// </summary>
    public bool? IsChecked
    {
        get => _isChecked;
        set
        {
            if (_isChecked != value)
            {
                _isChecked = value;
                OnPropertyChanged();
                
                // Update children when parent changes
                if (value.HasValue)
                {
                    foreach (var child in Children)
                    {
                        child.IsChecked = value;
                    }
                }
                
                // Update parent state
                UpdateParentCheckState();
            }
        }
    }

    /// <summary>
    /// Whether this is a folder node
    /// </summary>
    public bool IsFolder => Type.Equals("Folder", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Whether this is an album node
    /// </summary>
    public bool IsAlbum => Type.Equals("Album", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Display name for the tree view
    /// </summary>
    public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : UrlName ?? NodeId;

    /// <summary>
    /// Display text with counts for folders, or image count for albums
    /// </summary>
    public string DisplayText
    {
        get
        {
            if (IsAlbum)
            {
                return $"{DisplayName} ({ImageCount} images, {EstimatedSize})";
            }
            else
            {
                var totalImages = GetTotalImageCount();
                var totalSize = GetTotalSizeBytes();
                return $"{DisplayName} ({totalImages} images, {FormatBytes(totalSize)})";
            }
        }
    }

    /// <summary>
    /// Human-readable size estimate
    /// </summary>
    public string EstimatedSize => FormatBytes(EstimatedSizeBytes);

    /// <summary>
    /// Icon for the node type
    /// </summary>
    public string NodeIcon
    {
        get
        {
            if (IsAlbum)
            {
                if (Privacy == "Private") return "🔐";
                if (Privacy == "Unlisted") return "🔒";
                return "📷";
            }
            return IsExpanded ? "📂" : "📁";
        }
    }

    /// <summary>
    /// Get total image count including all children
    /// </summary>
    public int GetTotalImageCount()
    {
        var total = ImageCount;
        foreach (var child in Children)
        {
            total += child.GetTotalImageCount();
        }
        return total;
    }

    /// <summary>
    /// Get total size including all children
    /// </summary>
    public long GetTotalSizeBytes()
    {
        var total = EstimatedSizeBytes;
        foreach (var child in Children)
        {
            total += child.GetTotalSizeBytes();
        }
        return total;
    }

    /// <summary>
    /// Update parent checkbox state based on children
    /// </summary>
    private void UpdateParentCheckState()
    {
        if (Parent == null) return;

        var checkedChildren = Parent.Children.Count(c => c.IsChecked == true);
        var uncheckedChildren = Parent.Children.Count(c => c.IsChecked == false);
        var totalChildren = Parent.Children.Count;

        if (checkedChildren == totalChildren)
        {
            Parent._isChecked = true;
        }
        else if (uncheckedChildren == totalChildren)
        {
            Parent._isChecked = false;
        }
        else
        {
            Parent._isChecked = null; // Indeterminate
        }

        Parent.OnPropertyChanged(nameof(IsChecked));
        Parent.UpdateParentCheckState();
    }

    /// <summary>
    /// Get all selected albums (recursively)
    /// </summary>
    public List<SelectableFolderNode> GetSelectedAlbums()
    {
        var selected = new List<SelectableFolderNode>();
        
        if (IsAlbum && IsChecked == true)
        {
            selected.Add(this);
        }
        
        foreach (var child in Children)
        {
            selected.AddRange(child.GetSelectedAlbums());
        }
        
        return selected;
    }

    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
