<Window x:Class="Winmug.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Winmug.Converters"
        mc:Ignorable="d"
        Title="Winmug - SmugMug Photo Downloader"
        Height="700" Width="1000"
        MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource DarkBackground}"
        SizeToContent="Height">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Theme Toggle -->
        <Grid Grid.Row="0" Margin="0,0,0,30">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Title Section -->
            <StackPanel Grid.Column="0" Background="Transparent">
                <!-- WinMug Logo -->
                <Image Source="/Images/WinMugLogo.png"
                       Width="260"
                       Height="140"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,0"
                       Stretch="Uniform"
                       RenderOptions.BitmapScalingMode="HighQuality"
                       SnapsToDevicePixels="True"
                       UseLayoutRounding="True"/>
                <TextBlock Text="Download your entire SmugMug photo library to your local computer"
                           FontSize="16" Foreground="{DynamicResource SecondaryText}"
                           HorizontalAlignment="Center" Margin="0,-30,0,0"/>
            </StackPanel>

            <!-- Theme Toggle Button -->
            <Button Grid.Column="1" VerticalAlignment="Top" HorizontalAlignment="Right"
                    Command="{Binding ToggleThemeCommand}"
                    Width="40" Height="40" Padding="8"
                    Background="{DynamicResource CardBackground}"
                    BorderBrush="{DynamicResource BorderBrush}"
                    BorderThickness="1"
                    ToolTip="{Binding IsDarkTheme, Converter={x:Static local:ThemeTooltipConverter.Instance}}">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="20">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Button.Style>
                <TextBlock Text="{Binding IsDarkTheme, Converter={x:Static local:ThemeIconConverter.Instance}}"
                           FontSize="16" Foreground="{DynamicResource PrimaryText}"/>
            </Button>
        </Grid>

        <!-- Authentication Section with Dynamic Theme -->
        <Border Grid.Row="1" Background="{DynamicResource CardBackground}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                CornerRadius="8" Padding="24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="{Binding IsDarkTheme, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                  Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.5"/>
            </Border.Effect>
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- User Profile Section (when authenticated) -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <!-- Profile Picture with Blue -->
                        <Border Width="64" Height="64" CornerRadius="32" Background="{DynamicResource PrimaryBlue}" Margin="0,0,20,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#1E88E5" Direction="270" ShadowDepth="1" BlurRadius="4" Opacity="0.3"/>
                            </Border.Effect>
                            <TextBlock Text="{Binding UserDisplayName, Converter={x:Static local:FirstLetterConverter.Instance}}"
                                       FontSize="24" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <!-- User Info and Action Buttons -->
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="{Binding UserDisplayName}" FontSize="20" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                            <TextBlock Text="{Binding UserStatusText}" FontSize="14" Foreground="{DynamicResource SecondaryText}" Margin="0,4,0,12"/>

                            <!-- Action Buttons with Dynamic Theme -->
                            <StackPanel Orientation="Horizontal">
                                <Button Content="Show my albums"
                                        Command="{Binding LoadFolderStructureCommand}"
                                        Margin="0,0,12,0" Padding="16,8" FontSize="14" FontWeight="SemiBold"
                                        Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                </Button>
                                <Button Content="Logout"
                                        Command="{Binding LogoutCommand}"
                                        Padding="16,8" FontSize="14" FontWeight="SemiBold"
                                        Background="Transparent" Foreground="{DynamicResource SecondaryText}" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>

                    <!-- Authentication Prompt (when not authenticated) -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <TextBlock Text="Please authenticate to access your SmugMug photos"
                                   FontSize="18" Foreground="{DynamicResource SecondaryText}" HorizontalAlignment="Center" Margin="0,0,0,16"/>
                        <Button Content="Authenticate with SmugMug"
                                Command="{Binding InitiateAuthenticationCommand}"
                                Padding="24,12" FontSize="16" FontWeight="SemiBold"
                                Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <Border.Effect>
                                                        <DropShadowEffect Color="#1E88E5" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                                                    </Border.Effect>
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- Verification Code Input (only visible when waiting) -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0"
                            Visibility="{Binding IsWaitingForVerificationCode, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="Enter verification code:" VerticalAlignment="Center" Margin="0,0,12,0" FontSize="14" Foreground="{DynamicResource PrimaryText}"/>
                    <TextBox x:Name="VerificationCodeTextBox" Width="140" Margin="0,0,12,0" Padding="10,8" FontSize="14"
                             BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" Background="{DynamicResource CardBackground}" Foreground="{DynamicResource PrimaryText}"/>
                    <Button Content="Submit"
                            Command="{Binding CompleteAuthenticationCommand}"
                            CommandParameter="{Binding ElementName=VerificationCodeTextBox, Path=Text}"
                            Padding="16,8" Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0" FontSize="14" FontWeight="SemiBold">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>

                <!-- Progress Section with Green Dark Theme -->
                <Grid Margin="0,20,0,0"
                      Visibility="{Binding IsLoadingFolderStructure, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Status Message -->
                    <TextBlock Grid.Row="0" Text="{Binding StatusMessage}"
                               FontWeight="SemiBold" Margin="0,0,0,8" HorizontalAlignment="Center" FontSize="14" Foreground="{DynamicResource PrimaryText}"/>

                    <!-- Progress Bar with Blue -->
                    <ProgressBar Grid.Row="1" Value="{Binding AlbumsFoundCount}"
                                 Maximum="{Binding AlbumsFoundMaximum}"
                                 Height="8" Margin="0,0,0,6"
                                 Background="{DynamicResource ProgressBackground}" Foreground="{DynamicResource PrimaryBlue}"/>

                    <!-- Progress Text -->
                    <TextBlock Grid.Row="2" Text="{Binding AlbumDiscoveryProgress}"
                               HorizontalAlignment="Center" FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Albums Section with Dynamic Theme -->
        <Border Grid.Row="2" Background="{DynamicResource CardBackground}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                CornerRadius="8" Margin="0,0,0,16"
                Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border.Effect>
                <DropShadowEffect Color="{Binding IsDarkTheme, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                  Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.5"/>
            </Border.Effect>
            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Albums Header with Center-Aligned Controls -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,16">
                    <TextBlock Text="Albums" FontSize="20" FontWeight="SemiBold" Margin="0,0,24,0" VerticalAlignment="Center" Foreground="{DynamicResource PrimaryText}"/>
                    <Button Content="Select all" Command="{Binding SelectAllAlbumsCommand}"
                            Padding="12,6" Margin="0,0,12,0" FontSize="14" FontWeight="SemiBold"
                            Background="Transparent" Foreground="{DynamicResource PrimaryBlue}" BorderThickness="1" BorderBrush="{DynamicResource PrimaryBlue}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="Deselect all" Command="{Binding DeselectAllAlbumsCommand}"
                            Padding="12,6" FontSize="14" FontWeight="SemiBold"
                            Background="Transparent" Foreground="{DynamicResource SecondaryText}" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>

                <!-- Album Count Info -->
                <StackPanel Grid.Row="1" Orientation="Vertical" HorizontalAlignment="Center" Margin="0,0,0,16">
                    <!-- Total Albums -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,4">
                        <TextBlock Text="Total albums found: " FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding TotalAlbumCount}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                        <TextBlock Text=" (" FontSize="14" Foreground="{DynamicResource SecondaryText}" Margin="6,0,0,0"/>
                        <TextBlock Text="{Binding FolderStructure.TotalEstimatedSize, Mode=OneWay}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                        <TextBlock Text=")" FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                    </StackPanel>

                    <!-- Selected Albums -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="Selected for download: " FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding SelectedAlbumCount}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}"/>
                        <TextBlock Text=" albums (" FontSize="14" Foreground="{DynamicResource SecondaryText}" Margin="4,0,0,0"/>
                        <TextBlock Text="{Binding TotalSelectedImageCount}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}"/>
                        <TextBlock Text=" images, " FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding TotalSelectedSize}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}"/>
                        <TextBlock Text=")" FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                    </StackPanel>
                </StackPanel>

                <!-- Albums List with Dynamic Theme -->
                <Border Grid.Row="2" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="6" Background="{DynamicResource DarkBackground}">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled"
                                  CanContentScroll="True" Padding="4">
                        <ListView ItemsSource="{Binding AlbumsView}"
                                  ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                  ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                  VirtualizingPanel.IsVirtualizing="True"
                                  VirtualizingPanel.VirtualizationMode="Recycling"
                                  Background="Transparent" BorderThickness="0">
                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Setter Property="Padding" Value="0"/>
                                    <Setter Property="Margin" Value="0,0,0,2"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Background" Value="{DynamicResource CardBackground}"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="ListViewItem">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="4" Padding="12,8">
                                                    <Border.Effect>
                                                        <DropShadowEffect Color="{Binding DataContext.IsDarkTheme, RelativeSource={RelativeSource AncestorType=Window}, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                                                          Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                                                    </Border.Effect>
                                                    <ContentPresenter/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </ListView.ItemContainerStyle>
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Checkbox with Green -->
                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}"
                                                  VerticalAlignment="Center" Margin="0,0,12,0">
                                            <CheckBox.Style>
                                                <Style TargetType="CheckBox">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="CheckBox">
                                                                <Border Width="16" Height="16" Background="{DynamicResource DarkBackground}"
                                                                        BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="3">
                                                                    <TextBlock Text="✓" FontSize="12" Foreground="{DynamicResource PrimaryBlue}" FontWeight="Bold"
                                                                               HorizontalAlignment="Center" VerticalAlignment="Center"
                                                                               Visibility="{Binding IsChecked, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </CheckBox.Style>
                                        </CheckBox>

                                        <!-- Album Icon -->
                                        <TextBlock Grid.Column="1" Text="{Binding AlbumTypeIcon}"
                                                   FontSize="12" VerticalAlignment="Center" Margin="0,0,10,0" Foreground="{DynamicResource PrimaryBlue}"/>

                                        <!-- Album Name -->
                                        <TextBlock Grid.Column="2" Text="{Binding DisplayName}"
                                                   FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center"
                                                   TextTrimming="CharacterEllipsis" Foreground="{DynamicResource PrimaryText}"/>

                                        <!-- Image Count -->
                                        <TextBlock Grid.Column="3" VerticalAlignment="Center" Margin="12,0">
                                            <Run Text="{Binding ImageCount}" FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                                            <Run Text=" images" FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                                        </TextBlock>

                                        <!-- Size -->
                                        <TextBlock Grid.Column="4" Text="{Binding EstimatedSize}"
                                                   FontSize="12" Foreground="{DynamicResource SecondaryText}" VerticalAlignment="Center" Margin="12,0"/>

                                        <!-- Privacy & Selection Status -->
                                        <StackPanel Grid.Column="5" VerticalAlignment="Center" Orientation="Horizontal" Margin="12,0,0,0">
                                            <TextBlock Text="{Binding PrivacyIcon}" FontSize="12" Margin="0,0,6,0"
                                                       ToolTip="{Binding PrivacyStatus}" Foreground="{DynamicResource SecondaryText}"/>
                                            <Border Width="20" Height="20" Background="{DynamicResource PrimaryBlue}" CornerRadius="10"
                                                    Visibility="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <TextBlock Text="✓" FontSize="12" Foreground="White" FontWeight="Bold"
                                                           HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Border>

        <!-- Download Configuration Section with Dynamic Theme -->
        <Border Grid.Row="3" Background="{DynamicResource CardBackground}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                CornerRadius="8" Padding="24"
                Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border.Effect>
                <DropShadowEffect Color="{Binding IsDarkTheme, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                  Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.5"/>
            </Border.Effect>
            <StackPanel>
                <!-- Target Directory Section -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                    <TextBlock Text="Target Directory:" VerticalAlignment="Center" Width="140" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                    <TextBox Text="{Binding TargetDirectory}"
                             IsReadOnly="True"
                             Width="350" Margin="0,0,12,0" Padding="12,8" FontSize="14"
                             BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" Background="{DynamicResource DarkBackground}" Foreground="{DynamicResource PrimaryText}"/>
                    <Button Content="Browse..."
                            Command="{Binding SelectTargetDirectoryCommand}"
                            Padding="12,6" FontSize="14" FontWeight="SemiBold" Margin="0,0,12,0"
                            Background="Transparent" Foreground="{DynamicResource PrimaryBlue}" BorderThickness="1" BorderBrush="{DynamicResource PrimaryBlue}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Start Download Button (when not downloading) -->
                    <Button Content="Start download" FontSize="14" Padding="12,6" FontWeight="SemiBold"
                            Command="{Binding StartDownloadCommand}"
                            IsEnabled="{Binding CanStartDownload}"
                            Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0"
                            Visibility="{Binding IsDownloading, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Download Progress (when downloading) - Next to buttons -->
                    <StackPanel Orientation="Vertical" Margin="12,0,0,0" VerticalAlignment="Center"
                                Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding ProgressText}" FontSize="12" FontWeight="SemiBold"
                                   Foreground="{DynamicResource PrimaryText}" Margin="0,0,0,4"/>
                        <ProgressBar Width="200" Height="6" Value="{Binding OverallProgress}" Maximum="100"
                                     Background="{DynamicResource ProgressBackground}" Foreground="{DynamicResource PrimaryBlue}"/>
                    </StackPanel>
                </StackPanel>

                <!-- Download Progress Section (when downloading) -->
                <StackPanel HorizontalAlignment="Center" Margin="0,20,0,0"
                            Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="{Binding ProgressText}" FontSize="16" FontWeight="SemiBold"
                               HorizontalAlignment="Center" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryText}"/>
                    <ProgressBar Width="400" Height="8" Value="{Binding OverallProgress}" Maximum="100"
                                 Background="{DynamicResource ProgressBackground}" Foreground="{DynamicResource PrimaryBlue}" Margin="0,0,0,16"/>

                    <!-- Control Buttons (when downloading) -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="Pause" FontSize="14" Padding="12,6" Margin="0,0,8,0" FontWeight="SemiBold"
                                Command="{Binding PauseDownloadCommand}"
                                IsEnabled="{Binding CanPauseDownload}"
                                Background="#FFA500" Foreground="White" BorderThickness="0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>

                        <Button Content="Cancel" FontSize="14" Padding="12,6" FontWeight="SemiBold"
                                Command="{Binding CancelDownloadCommand}"
                                IsEnabled="{Binding CanCancelDownload}"
                                Background="Transparent" Foreground="#DC3545" BorderThickness="1" BorderBrush="#DC3545">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
