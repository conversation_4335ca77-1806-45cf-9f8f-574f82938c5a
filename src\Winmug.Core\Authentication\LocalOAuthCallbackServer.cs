using System.Net;
using System.Text;
using Microsoft.Extensions.Logging;

namespace Winmug.Core.Authentication;

/// <summary>
/// Local HTTP server for handling OAuth callbacks automatically
/// </summary>
public class LocalOAuthCallbackServer : IDisposable
{
    private readonly HttpListener _listener;
    private readonly ILogger<LocalOAuthCallbackServer> _logger;
    private readonly TaskCompletionSource<OAuthCallbackResult> _callbackReceived;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private bool _disposed;

    public LocalOAuthCallbackServer(int port, ILogger<LocalOAuthCallbackServer> logger)
    {
        _logger = logger;
        _callbackReceived = new TaskCompletionSource<OAuthCallbackResult>();
        _cancellationTokenSource = new CancellationTokenSource();
        
        _listener = new HttpListener();
        _listener.Prefixes.Add($"http://localhost:{port}/");
        
        _logger.LogInformation("Local OAuth callback server initialized on port {Port}", port);
    }

    /// <summary>
    /// Starts the server and waits for OAuth callback
    /// </summary>
    /// <param name="timeoutSeconds">Maximum time to wait for callback</param>
    /// <returns>OAuth callback result</returns>
    public async Task<OAuthCallbackResult> StartAndWaitForCallbackAsync(int timeoutSeconds = 300)
    {
        try
        {
            _logger.LogInformation("Attempting to start local OAuth callback server...");

            try
            {
                _listener.Start();
                _logger.LogInformation("✅ Local OAuth callback server started successfully, waiting for callback...");
            }
            catch (HttpListenerException ex) when (ex.ErrorCode == 5) // Access denied
            {
                _logger.LogError("❌ Access denied starting HTTP listener. This usually means administrator privileges are required.");
                _logger.LogError("💡 Try running WinMug as administrator, or use manual authentication instead.");
                return new OAuthCallbackResult
                {
                    Success = false,
                    Error = "Access denied - administrator privileges may be required for automatic authentication"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Failed to start HTTP listener: {Message}", ex.Message);
                return new OAuthCallbackResult
                {
                    Success = false,
                    Error = $"Failed to start callback server: {ex.Message}"
                };
            }

            // Start listening for requests in background
            _ = Task.Run(ListenForCallbackAsync, _cancellationTokenSource.Token);

            // Wait for callback with timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                _cancellationTokenSource.Token, timeoutCts.Token);

            try
            {
                var result = await _callbackReceived.Task.WaitAsync(combinedCts.Token);
                _logger.LogInformation("✅ OAuth callback received successfully");
                return result;
            }
            catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
            {
                _logger.LogWarning("⏰ OAuth callback timed out after {Timeout} seconds", timeoutSeconds);
                return new OAuthCallbackResult
                {
                    Success = false,
                    Error = "Timeout waiting for OAuth callback"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in OAuth callback server: {Message}", ex.Message);
            return new OAuthCallbackResult
            {
                Success = false,
                Error = ex.Message
            };
        }
        finally
        {
            Stop();
        }
    }

    private async Task ListenForCallbackAsync()
    {
        try
        {
            while (_listener.IsListening && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                var context = await _listener.GetContextAsync();
                _ = Task.Run(() => HandleCallbackAsync(context), _cancellationTokenSource.Token);
            }
        }
        catch (ObjectDisposedException)
        {
            // Expected when stopping
        }
        catch (HttpListenerException ex) when (ex.ErrorCode == 995) // ERROR_OPERATION_ABORTED
        {
            // Expected when stopping
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listening for OAuth callback");
            _callbackReceived.TrySetResult(new OAuthCallbackResult
            {
                Success = false,
                Error = ex.Message
            });
        }
    }

    private async Task HandleCallbackAsync(HttpListenerContext context)
    {
        try
        {
            var request = context.Request;
            var response = context.Response;

            _logger.LogInformation("Received OAuth callback: {Url}", request.Url);

            // Parse query parameters
            var query = request.Url?.Query;
            if (string.IsNullOrEmpty(query))
            {
                await SendErrorResponseAsync(response, "No query parameters received");
                return;
            }

            var queryParams = System.Web.HttpUtility.ParseQueryString(query);
            var oauthToken = queryParams["oauth_token"];
            var oauthVerifier = queryParams["oauth_verifier"];
            var error = queryParams["error"];

            if (!string.IsNullOrEmpty(error))
            {
                _logger.LogWarning("OAuth error received: {Error}", error);
                await SendErrorResponseAsync(response, $"OAuth error: {error}");
                _callbackReceived.TrySetResult(new OAuthCallbackResult
                {
                    Success = false,
                    Error = error
                });
                return;
            }

            if (string.IsNullOrEmpty(oauthToken) || string.IsNullOrEmpty(oauthVerifier))
            {
                await SendErrorResponseAsync(response, "Missing oauth_token or oauth_verifier");
                _callbackReceived.TrySetResult(new OAuthCallbackResult
                {
                    Success = false,
                    Error = "Missing required OAuth parameters"
                });
                return;
            }

            // Send success response to browser
            await SendSuccessResponseAsync(response);

            // Signal successful callback
            _callbackReceived.TrySetResult(new OAuthCallbackResult
            {
                Success = true,
                OAuthToken = oauthToken,
                OAuthVerifier = oauthVerifier
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling OAuth callback");
            _callbackReceived.TrySetResult(new OAuthCallbackResult
            {
                Success = false,
                Error = ex.Message
            });
        }
    }

    private async Task SendSuccessResponseAsync(HttpListenerResponse response)
    {
        var html = @"
<!DOCTYPE html>
<html>
<head>
    <title>Winmug - Authentication Successful</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-size: 24px; margin-bottom: 20px; }
        .message { color: #333; font-size: 16px; line-height: 1.5; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='success'>✓ Authentication Successful!</div>
        <div class='message'>
            You have successfully authenticated with SmugMug.<br>
            You can now close this browser window and return to Winmug.
        </div>
    </div>
    <script>
        // Auto-close after 3 seconds
        setTimeout(function() { window.close(); }, 3000);
    </script>
</body>
</html>";

        var buffer = Encoding.UTF8.GetBytes(html);
        response.ContentType = "text/html; charset=utf-8";
        response.ContentLength64 = buffer.Length;
        response.StatusCode = 200;
        
        await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
        response.OutputStream.Close();
    }

    private async Task SendErrorResponseAsync(HttpListenerResponse response, string error)
    {
        var html = $@"
<!DOCTYPE html>
<html>
<head>
    <title>Winmug - Authentication Error</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f5f5f5; }}
        .container {{ max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .error {{ color: #dc3545; font-size: 24px; margin-bottom: 20px; }}
        .message {{ color: #333; font-size: 16px; line-height: 1.5; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='error'>❌ Authentication Error</div>
        <div class='message'>
            {error}<br><br>
            Please close this window and try again in Winmug.
        </div>
    </div>
</body>
</html>";

        var buffer = Encoding.UTF8.GetBytes(html);
        response.ContentType = "text/html; charset=utf-8";
        response.ContentLength64 = buffer.Length;
        response.StatusCode = 400;
        
        await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
        response.OutputStream.Close();
    }

    public void Stop()
    {
        if (_disposed) return;

        try
        {
            _cancellationTokenSource.Cancel();
            _listener.Stop();
            _logger.LogInformation("Local OAuth callback server stopped");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error stopping OAuth callback server");
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        Stop();
        _cancellationTokenSource.Dispose();
        _listener.Close();
    }
}

/// <summary>
/// Result of OAuth callback
/// </summary>
public class OAuthCallbackResult
{
    public bool Success { get; set; }
    public string? OAuthToken { get; set; }
    public string? OAuthVerifier { get; set; }
    public string? Error { get; set; }
}
